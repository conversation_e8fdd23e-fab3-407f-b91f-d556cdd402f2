{"name": "gruul-mall-view-supplier", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "start": "vite --mode dev", "prod": "vite --mode test", "build:test": "vite build --mode test", "build:dev": "vite build --mode dev", "build": "vite build --mode prod", "serve": "vite preview", "prepare": "husky install", "fix": "eslint --fix --ext .js,.jsx,.ts,.tsx,.vue,.html src --config .eslintrc.cjs", "lint": "eslint --ext .js,.jsx,.ts,.tsx,.vue,.html src --config .eslintrc.cjs", "log": "conventional-changelog -p custom-config -i CHANGELOG.md -s -r 0  -n ./changelog-option.js", "commit": "git-cz", "test": "vitest", "coverage": "vitest run --coverage"}, "lint-staged": {"src/**/*.{js,jsx,ts,.tsx,vue,html,md}": "eslint --config .eslintrc.cjs", "*.{ts,tsx,js,json,html,yml,css,less,md}": "prettier --write"}, "changelog": {"baseUrl": "http://*************:8020", "bugsUrl": "http://*************:8020/mid-platform-view/gruul-mall-view-shop/issues", "emojis": true, "authorName": true, "authorEmail": true}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "^1.1.1", "@vueuse/core": "^8.3.1", "@wangeditor/editor": "^5.0.1", "@wangeditor/editor-for-vue": "^5.1.11", "axios": "^0.21.4", "crypto-js": "^4.2.0", "decimal.js": "^10.3.1", "echarts": "^5.4.0", "element-china-area-data": "^5.0.2", "element-plus": "^2.1.1", "elui-china-area-dht": "^2.0.0", "howler": "^2.2.3", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mitt": "^3.0.0", "pinia": "^2.0.17", "sass": "^1.38.2", "sortablejs": "^1.15.6", "stompjs": "^2.3.3", "terser": "^5.31.6", "vant": "^3.6.1", "vue": "^3.3.4", "vue-clipboard3": "^2.0.0", "vue-cropperjs": "5.0.0", "vue-dompurify-html": "^3.0.0", "vue-draggable-next": "^2.1.1", "vue-router": "4.3.2", "vue3-draggable-resizable": "^1.6.4"}, "devDependencies": {"@commitlint/cli": "^16.2.4", "@commitlint/config-conventional": "^16.2.4", "@iconify-json/ep": "^1.2.2", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.6", "@types/mockjs": "^1.0.7", "@types/node": "^22.8.1", "@types/stompjs": "^2.3.5", "@types/sortablejs": "^1.15.8", "@types/vue-cropperjs": "^4.1.2", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "@vitejs/plugin-vue": "^3.2.0", "@vitejs/plugin-vue-jsx": "^1.3.10", "@vue/compiler-sfc": "^3.2.6", "@vue/test-utils": "^2.2.6", "autoprefixer": "^10.4.5", "commitizen": "^4.2.4", "conventional-changelog-cli": "^2.2.2", "conventional-changelog-custom-config": "^0.3.1", "eslint": "^8.15.0", "eslint-config-prettier": "^8.5.0", "eslint-define-config": "^1.4.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.7.1", "happy-dom": "^7.7.2", "husky": "^8.0.0", "lint-staged": "^12.4.1", "mockjs": "^1.1.0", "msw": "^1.1.0", "prettier": "^2.6.2", "typescript": "^4.3.2", "unplugin-auto-import": "^0.11.4", "unplugin-icons": "^0.14.13", "unplugin-vue-components": "^0.22.4", "vite": "^3.2.3", "vite-auto-import-resolvers": "^3.0.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-inspect": "^0.7.7", "vite-plugin-vue-setup-extend": "^0.4.0", "vitest": "^0.25.5", "vue-tsc": "^0.2.2"}, "volta": {"node": "16.15.0", "npm": "8.5.5"}}